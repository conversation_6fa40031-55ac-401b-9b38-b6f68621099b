# CampusGuard - 智能校园网络安全监控平台 PRD

## 1. 产品概述

### 1.1 产品名称
**CampusGuard** - 基于AI的校园网络安全实时监控系统

### 1.2 产品定位
智能校园网络安全监控平台是一个集网络设备监控、安全威胁检测、智能分析和可视化展示于一体的综合性网络安全管理系统。

### 1.3 产品目标
- **主要目标**：为校园网络提供7×24小时实时安全监控和威胁检测
- **业务价值**：提升网络安全防护能力，减少人工监控工作量80%，威胁检测准确率≥80%
- **技术目标**：构建基于AI智能分析的混合检测模式，支持自然语言交互的运维体验

### 1.4 目标用户
- **主要用户**：校园网络管理员、IT运维人员
- **次要用户**：网络安全专员、系统管理员
- **决策用户**：信息化部门负责人、CIO

## 2. 用户需求分析

### 2.1 核心用户故事

#### 2.1.1 网络管理员
- **As a** 网络管理员，**I want to** 实时监控所有网络设备状态，**So that** 我能及时发现设备故障和性能问题
- **As a** 网络管理员，**I want to** 通过可视化大屏查看网络安全态势，**So that** 我能快速了解整体安全状况
- **As a** 网络管理员，**I want to** 接收智能告警通知，**So that** 我能优先处理重要安全事件

#### 2.1.2 安全运维人员
- **As a** 安全运维人员，**I want to** 使用自然语言查询安全事件，**So that** 我能快速获取所需信息
- **As a** 安全运维人员，**I want to** 获得AI生成的威胁分析报告，**So that** 我能更好地理解和应对安全威胁
- **As a** 安全运维人员，**I want to** 自定义告警规则，**So that** 我能根据实际需求调整监控策略

### 2.2 用户痛点
1. **监控盲区**：传统监控工具覆盖不全面，存在监控死角
2. **告警疲劳**：大量误报导致重要告警被忽略
3. **分析复杂**：缺乏智能分析能力，需要人工判断威胁
4. **响应滞后**：发现威胁到响应处置时间过长
5. **运维复杂**：需要专业技能才能有效使用监控系统

## 3. 功能需求规格说明

### 3.1 核心功能模块

#### 3.1.1 网络数据采集模块
**功能描述**：基于标准网络协议采集校园网络设备状态和流量数据

**详细需求**：
- **SNMP数据采集**：
  - 支持SNMP v2c协议
  - 采集频率：设备状态5分钟/次，接口流量1分钟/次
  - 监控对象：交换机、路由器、无线AP、服务器
  - 关键指标：系统信息、接口统计、性能指标
- **网络设备发现**：
  - 自动发现网络设备
  - 支持ARP表分析、SNMP Walk、端口扫描
  - 新设备自动添加到监控列表
- **流量监控**：
  - 基于SNMP接口统计的流量分析
  - 带宽利用率计算和趋势分析
  - 异常流量检测

**验收标准**：
- 数据采集成功率：≥95%
- 数据处理延迟：≤60秒
- 并发设备支持：≥100台

#### 3.1.2 安全检测引擎
**功能描述**：基于网络流量分析和规则匹配的安全威胁检测

**详细需求**：
- **流量异常检测**：
  - 7天历史数据计算正常流量基线
  - 动态阈值：基线值 × 3倍标准差
  - 检测类型：流量突增、带宽异常、连接数异常
- **设备状态异常检测**：
  - CPU使用率>90%持续5分钟触发告警
  - 内存使用率>85%持续5分钟触发告警
  - 设备离线检测和接口异常检测
- **网络攻击检测**：
  - 端口扫描检测：5分钟内访问>20个端口
  - DDoS攻击检测：多维度检测（流量、连接、源IP）
  - 静态黑名单检测和DNS异常检测

**验收标准**：
- 威胁检测准确率：≥80%
- 误报率：≤15%
- 检测延迟：≤5分钟

#### 3.1.3 智能分析引擎（OpenAI Agents + DeepSeek V3）
**功能描述**：基于OpenAI Agents框架集成DeepSeek V3模型的智能威胁分析和自然语言交互

**详细需求**：
- **AI异常分析Agent**：
  - 框架：openai-agents-python
  - 模型：DeepSeek V3 API
  - 角色：网络安全分析专家
  - 能力：模式识别、异常关联、威胁评估
  - 工具函数：网络数据查询、设备状态检查、威胁情报查询
  - 输出：自然语言异常报告和处置建议
- **对话式运维Agent**：
  - 框架：openai-agents-python
  - 模型：DeepSeek V3 API
  - 支持自然语言查询："今天有哪些高危告警？"
  - 智能问答基于告警历史和知识库
  - 提供个性化处置步骤和预期效果
- **告警解释Agent**：
  - 框架：openai-agents-python
  - 模型：DeepSeek V3 API
  - 生成威胁行为的详细解释
  - 提供影响分析和风险评估
  - 智能关联相关安全事件

**验收标准**：
- AI分析响应时间：≤30秒
- 自然语言查询准确率：≥85%
- 处置建议有效性：≥75%

#### 3.1.4 告警规则引擎
**功能描述**：智能告警生成、分级处理和降噪优化

**详细需求**：
- **告警分级标准**：
  - 紧急(Critical)：系统瘫痪、数据泄露、大规模攻击
  - 高危(High)：重要系统异常、已知威胁、权限提升
  - 中危(Medium)：可疑行为、配置异常、性能问题
  - 低危(Low)：信息收集、轻微异常、策略违规
- **告警降噪算法**：
  - 时间窗口聚合：5分钟内相同类型告警合并
  - 频率阈值过滤：同一源IP 1小时内告警>10次则聚合
  - 相关性分析和白名单过滤
- **通知机制**：
  - 多渠道通知：邮件、短信、语音播报、Webhook
  - 优先级队列确保重要告警优先处理

**验收标准**：
- 告警响应时间：≤30秒
- 告警准确率：≥90%
- 误报率：≤10%

#### 3.1.5 可视化监控大屏
**功能描述**：实时网络安全态势可视化展示和交互操作

**详细需求**：
- **网络拓扑图**：
  - 显示设备连接关系、实时状态、流量方向
  - 支持点击查看详情、拖拽调整布局
  - 状态指示：绿色(正常)、黄色(警告)、红色(异常)、灰色(离线)
- **威胁态势地图**：
  - 攻击源地理位置和攻击目标分布
  - 实时动画展示攻击流向和威胁强度变化
- **实时监控仪表盘**：
  - 关键指标：在线设备数、告警数量、流量统计、威胁等级
  - 趋势图表：24小时流量趋势、告警趋势、威胁类型分布
- **告警管理界面**：
  - 实时告警列表、历史告警、处理状态
  - 支持批量操作和详情查看

**验收标准**：
- 数据刷新延迟：≤5秒
- 页面加载时间：≤3秒
- 支持单用户访问（简化架构）

### 3.2 系统管理功能

#### 3.2.1 设备管理模块
- **设备信息管理**：IP地址、MAC地址、设备类型、厂商型号
- **设备配置管理**：SNMP配置、监控参数、告警阈值
- **设备分组管理**：按楼层、部门、功能进行设备分组
- **设备操作管理**：支持批量导入、单个添加、配置修改

#### 3.2.2 配置管理模块
- **告警规则配置**：规则模板管理、自定义规则、规则优先级
- **系统参数设置**：监控参数、通知设置
- **配置版本管理**：配置备份、版本对比、配置回滚

## 4. 非功能性需求

### 4.1 性能需求
- **响应时间**：
  - 页面加载时间：≤3秒
  - 数据查询响应：≤2秒
  - 告警生成延迟：≤30秒
- **并发性能**：
  - 支持100台设备同时监控
  - 支持单用户访问（简化架构）
  - 支持1000条/小时告警处理
- **数据处理能力**：
  - 数据采集延迟：≤60秒
  - 数据存储：支持1年历史数据
  - 实时数据缓存：TTL=1小时

### 4.2 可用性需求
- **系统可用性**：≥99.5%
- **故障恢复时间**：≤30分钟
- **数据备份**：每日自动备份，支持快速恢复
- **容错机制**：关键组件支持故障转移

### 4.3 数据完整性需求
- **数据完整性**：确保监控数据的准确性和完整性

### 4.4 兼容性需求
- **浏览器兼容**：支持Chrome、Firefox、Safari、Edge最新版本
- **设备兼容**：支持华为厂商网络设备
- **协议兼容**：支持SNMP v2c、HTTP、WebSocket
- **分辨率适配**：支持1920x1080、2560x1440、4K分辨率、移动端适配

## 5. 技术架构和实现方案

### 5.1 系统架构设计
采用分层架构设计，包含以下层次：
- **网络设备层**：交换机、路由器、无线AP、服务器
- **数据采集层**：SNMP采集器、网络扫描器、流量监控器
- **数据处理层**：数据清洗、规则引擎、异常检测
- **智能分析层**：openai-agents-python框架 + DeepSeek V3模型
- **数据存储层**：MySQL数据库（统一存储）
- **应用服务层**：FastAPI后端、WebSocket服务
- **用户界面层**：Vue.js Web界面、监控大屏
- **告警通知层**：多渠道告警通知机制

### 5.2 技术栈规格

#### 5.2.1 后端技术栈
- **编程语言**：Python 3.13.2
- **依赖管理**：pip + requirements.txt
- **虚拟环境**：venv（强制使用）
- **Web框架**：FastAPI 0.115.14
- **数据库**：MySQL 8.0
- **网络协议库**：
  - pysnmp（SNMP协议实现）
  - python3-nmap 1.9.0+（网络扫描）
  - psutil 7.0.0（系统监控）
- **AI分析引擎**：
  - openai-agents-python 0.1.0
  - DeepSeek V3 API（模型名称:deepseek-chat）
  - 支持自然语言查询和威胁分析
  - 成本优化的本地化AI解决方案
- **数据处理**：
  - pandas 2.3.0（数据分析）
  - numpy 2.3.1（数值计算）

#### 5.2.2 前端技术栈
- **前端框架**：Vue.js 3.5.17
- **UI组件库**：Element Plus 2.10.2
- **AI对话组件**：ant-design-x-vue 1.2.7（专业AI对话界面）
- **图表库**：ECharts 5.6.0
- **网络拓扑图**：@antv/g6 5.0.49（蚂蚁金服图可视化引擎）
- **样式框架**：Tailwind CSS 4.1.11
- **构建工具**：Vite 7.0.0
- **状态管理**：Pinia 3.0.3
- **路由管理**：Vue Router 4.5.1

#### 5.2.3 前端package.json配置
```json
{
  "name": "campusguard-frontend",
  "version": "1.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "vue-tsc && vite build",
    "preview": "vite preview"
  },
  "dependencies": {
    "vue": "^3.5.17",
    "vue-router": "^4.5.1",
    "pinia": "^3.0.3",
    "element-plus": "^2.10.2",
    "ant-design-x-vue": "^1.2.7",
    "@antv/g6": "^5.0.49",
    "echarts": "^5.6.0",
    "vue-echarts": "^7.0.4",
    "axios": "^1.8.4",
    "@element-plus/icons-vue": "^2.3.1",
    "tailwindcss": "^4.1.11"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^5.0.1",
    "vite": "^7.0.0",
    "autoprefixer": "^10.4.21",
    "postcss": "^8.5.6"
  }
}

#### 5.2.4 G6网络拓扑图实现示例
```vue
<template>
  <div class="network-topology">
    <div ref="graphContainer" class="graph-container"></div>
    <div class="topology-controls">
      <el-button @click="refreshTopology">刷新拓扑</el-button>
      <el-button @click="resetLayout">重置布局</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { Graph } from '@antv/g6'

const graphContainer = ref<HTMLDivElement>()
let graph: Graph | null = null

const initGraph = () => {
  if (!graphContainer.value) return

  graph = new Graph({
    container: graphContainer.value,
    width: 1200,
    height: 800,
    // G6 5.x 新的行为配置方式
    behaviors: ['zoom-canvas', 'drag-canvas', 'drag-element'],
    layout: {
      type: 'force',
      preventOverlap: true,
      nodeSize: 60,
      linkDistance: 150
    },
    // G6 5.x 新的节点样式配置
    node: {
      style: {
        size: 60,
        fill: '#5B8FF9',
        stroke: '#5B8FF9',
        lineWidth: 2,
        labelText: (d: any) => d.label,
        labelFill: '#000',
        labelFontSize: 12,
        labelPosition: 'bottom'
      },
      // 节点状态样式
      state: {
        hover: {
          fill: '#1890ff',
          stroke: '#1890ff'
        },
        selected: {
          fill: '#722ed1',
          stroke: '#722ed1',
          lineWidth: 3
        }
      }
    },
    // G6 5.x 新的边样式配置
    edge: {
      style: {
        stroke: '#e2e2e2',
        lineWidth: 2,
        endArrow: true
      },
      state: {
        hover: {
          stroke: '#1890ff',
          lineWidth: 3
        }
      }
    }
  })

  loadNetworkData()
}

const loadNetworkData = async () => {
  try {
    const response = await fetch('/api/v1/topology/data')
    const data = await response.json()

    const nodes = data.devices.map((device: any) => ({
      id: device.id,
      label: device.name,
      data: {
        deviceType: device.type,
        status: device.status,
        ip: device.ip,
        location: device.location
      },
      style: {
        fill: getDeviceColor(device.status),
        stroke: getDeviceColor(device.status)
      }
    }))

    const edges = data.connections.map((conn: any) => ({
      id: `${conn.source}-${conn.target}`,
      source: conn.source,
      target: conn.target,
      data: {
        bandwidth: conn.bandwidth,
        latency: conn.latency
      },
      style: {
        stroke: conn.status === 'active' ? '#52c41a' : '#ff4d4f',
        lineWidth: conn.bandwidth > 1000 ? 4 : 2
      }
    }))

    // G6 5.x 新的数据设置方式
    graph?.setData({ nodes, edges })
    graph?.render()
  } catch (error) {
    console.error('加载网络拓扑数据失败:', error)
  }
}

const getDeviceColor = (status: string) => {
  switch (status) {
    case 'online': return '#52c41a'
    case 'warning': return '#faad14'
    case 'error': return '#ff4d4f'
    default: return '#d9d9d9'
  }
}

// 刷新拓扑数据
const refreshTopology = async () => {
  await loadNetworkData()
}

// 重置布局
const resetLayout = () => {
  graph?.layout()
}

// 添加节点点击事件处理
const setupGraphEvents = () => {
  if (!graph) return

  // 节点点击事件
  graph.on('node:click', (event: any) => {
    const { item } = event
    const nodeData = item.getModel()
    console.log('点击设备:', nodeData)

    // 可以在这里打开设备详情弹窗
    // showDeviceDetail(nodeData.data)
  })

  // 边点击事件
  graph.on('edge:click', (event: any) => {
    const { item } = event
    const edgeData = item.getModel()
    console.log('点击连接:', edgeData)
  })
}

onMounted(() => {
  initGraph()
  setupGraphEvents()
})

onUnmounted(() => {
  graph?.destroy()
})
</script>

<style scoped>
.network-topology {
  width: 100%;
  height: 100%;
  position: relative;
}

.graph-container {
  width: 100%;
  height: calc(100% - 60px);
  border: 1px solid #e8e8e8;
  border-radius: 4px;
}

.topology-controls {
  height: 60px;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 0 16px;
  background: #fafafa;
  border-top: 1px solid #e8e8e8;
}
</style>
```

#### 5.2.5 部署和运维
- **容器化**：Docker、Docker Compose
- **Web服务器**：Nginx

#### 5.2.6 完整requirements.txt
```txt
# Web框架
fastapi==0.115.14
uvicorn[standard]==0.35.0
websockets==15.0.1

# 数据库
mysql-connector-python==9.3.0
SQLAlchemy==2.0.41
alembic==1.16.2

# 网络协议
pysnmp>=6.0.0
python3-nmap>=1.9.0
psutil==7.0.0

# 数据处理
pandas==2.3.0
numpy==2.3.1
scipy==1.16.0

# AI分析
openai-agents-python==0.1.0
httpx==0.28.1
pydantic==2.11.7

# 安全认证
python-jose[cryptography]==3.5.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.20

# 配置管理
python-dotenv==1.1.1
pyyaml==6.0.2

# 日志和监控
loguru==0.7.3
```

### 5.3 数据库设计

#### 5.3.1 MySQL 8.0 统一数据存储
**核心表结构**：
- **设备信息表**：devices, device_groups, device_configs
- **监控数据表**：monitoring_data, performance_metrics, traffic_stats
- **告警记录表**：alerts, alert_rules, alert_notifications
- **系统配置表**：system_configs, rule_templates

**实时数据存储策略**：
- **实时监控数据表**：real_time_metrics（替代Redis缓存）
  - 使用MySQL的MEMORY存储引擎提高查询性能
  - 设置TTL机制自动清理过期数据
  - 索引优化确保毫秒级查询响应

- **配置缓存表**：config_cache
  - 缓存系统配置和规则信息
  - 定期刷新机制保证数据一致性

**性能优化策略**：
- **分区表**：按时间分区存储历史监控数据
- **索引优化**：为高频查询字段建立复合索引
- **连接池**：使用连接池管理数据库连接
- **读写分离**：主从复制实现读写分离

### 5.4 API接口设计
- **设备管理API**：GET/POST/PUT/DELETE /api/v1/devices
- **监控数据API**：GET /api/v1/monitoring/data
- **告警管理API**：GET/POST/PUT /api/v1/alerts
- **OpenAI Agents API**：POST /api/v1/agents/analyze
- **AI对话API**：POST /api/v1/agents/chat
- **配置管理API**：GET/POST/PUT /api/v1/configs
- **实时数据API**：GET /api/v1/realtime/metrics（替代Redis缓存查询）

## 6. 详细技术规格

### 6.1 OpenAI Agents + DeepSeek V3 AI引擎实现细节

#### 6.1.1 OpenAI Agents架构设计
- **安全分析Agent**：
  - 框架：openai-agents-python
  - 模型：DeepSeek V3 API（通过自定义客户端）
  - 角色定义：网络安全专家，专注威胁识别和风险评估
  - 工具函数：网络数据查询、设备状态检查、威胁情报查询
  - 输出格式：结构化威胁报告 + 自然语言解释
  - 优势：Agent框架的工具调用能力 + DeepSeek V3的成本优势

- **运维助手Agent**：
  - 框架：openai-agents-python
  - 模型：DeepSeek V3 API
  - 角色定义：网络运维专家，提供运维建议和故障诊断
  - 交互方式：自然语言对话，支持ant-design-x-vue组件
  - 知识库：网络设备手册、故障处理流程、最佳实践
  - 特色功能：支持中文对话，更适合国内用户

- **告警解释Agent**：
  - 框架：openai-agents-python
  - 模型：DeepSeek V3 API
  - 功能：生成告警的详细解释和影响分析
  - 关联分析：基于攻击链的相关告警关联
  - 处置建议：个性化的处置步骤和预期效果

#### 6.1.2 OpenAI Agents + DeepSeek V3集成配置
```python
from openai import AsyncOpenAI
from agents import Agent, set_default_openai_client, function_tool

# 配置DeepSeek V3作为OpenAI兼容的客户端
deepseek_client = AsyncOpenAI(
    base_url="https://api.deepseek.com/v1",
    api_key="your_deepseek_api_key"
)

# 设置为默认客户端
set_default_openai_client(deepseek_client)

# 定义网络监控工具函数
@function_tool
async def get_device_status(device_id: str) -> str:
    """获取设备状态信息"""
    # 实际实现中查询数据库
    return f"设备 {device_id} 状态正常"

@function_tool
async def query_alerts(severity: str = "high") -> str:
    """查询告警信息"""
    # 实际实现中查询告警数据
    return f"当前有3个{severity}级别告警"

# 创建安全分析Agent
security_agent = Agent(
    name="Security Analyst",
    instructions="你是网络安全专家，专注于威胁识别和风险评估。使用提供的工具查询设备状态和告警信息。",
    model="deepseek-v3",  # 使用DeepSeek V3模型
    tools=[get_device_status, query_alerts]
)

# 成本控制策略
COST_CONTROL = {
    "daily_token_limit": 1000000,  # 每日token限制
    "cache_duration": 3600,        # 缓存1小时
    "batch_processing": True,      # 批量处理
    "priority_queue": True         # 优先级队列
}
```

#### 6.1.3 AI对话界面实现（OpenAI Agents + ant-design-x-vue）
```vue
<!-- 使用ant-design-x-vue + OpenAI Agents实现AI对话 -->
<template>
  <div class="ai-chat-container">
    <!-- 消息列表区域 -->
    <div class="messages-container" ref="messagesContainer">
      <AXBubble
        v-for="message in messages"
        :key="message.id"
        :content="message.content"
        :type="message.type"
        :avatar="message.avatar"
        :status="message.status"
        :actions="message.actions"
        :typing="message.typing"
        @action-click="handleActionClick"
      />

      <!-- 加载状态指示器 -->
      <AXBubble
        v-if="isLoading"
        type="ai"
        avatar="/ai-avatar.png"
        :typing="true"
        content="正在思考中..."
      />
    </div>

    <!-- 输入区域 -->
    <div class="input-container">
      <!-- 快捷建议 -->
      <AXSuggestion
        v-if="showSuggestions"
        :items="suggestions"
        @trigger="handleSuggestionClick"
      />

      <!-- 输入框 -->
      <AXPrompt
        v-model="inputValue"
        :loading="isLoading"
        :placeholder="placeholder"
        :actions="promptActions"
        @submit="handleSubmit"
        @action-click="handlePromptAction"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  Bubble as AXBubble,
  Prompt as AXPrompt,
  Suggestion as AXSuggestion
} from 'ant-design-x-vue'
import { ref, nextTick, computed } from 'vue'

interface Message {
  id: number
  content: string
  type: 'user' | 'ai'
  avatar: string
  status?: 'success' | 'error' | 'loading'
  actions?: Array<{ key: string; label: string; icon?: string }>
  typing?: boolean
}

const messages = ref<Message[]>([])
const inputValue = ref('')
const isLoading = ref(false)
const messagesContainer = ref<HTMLDivElement>()
const showSuggestions = ref(true)

// 动态占位符
const placeholder = computed(() => {
  const placeholders = [
    '请输入您的问题，例如：今天有哪些高危告警？',
    '您可以问我：网络设备状态如何？',
    '试试问：最近有什么安全威胁？',
    '您可以询问：系统性能怎么样？'
  ]
  return placeholders[Math.floor(Math.random() * placeholders.length)]
})

// 快捷建议
const suggestions = ref([
  { key: 'alerts', label: '查看高危告警', icon: 'warning' },
  { key: 'devices', label: '设备状态检查', icon: 'desktop' },
  { key: 'network', label: '网络性能分析', icon: 'wifi' },
  { key: 'security', label: '安全威胁评估', icon: 'shield' }
])

// 输入框操作按钮
const promptActions = ref([
  { key: 'clear', label: '清空', icon: 'clear' },
  { key: 'voice', label: '语音输入', icon: 'audio' }
])

const handleSubmit = async (value: string) => {
  if (!value.trim()) return

  isLoading.value = true
  showSuggestions.value = false

  // 添加用户消息
  const userMessage: Message = {
    id: Date.now(),
    content: value,
    type: 'user',
    avatar: '/user-avatar.png'
  }
  messages.value.push(userMessage)

  // 滚动到底部
  await nextTick()
  scrollToBottom()

  try {
    // 调用OpenAI Agents API
    const response = await fetch('/api/v1/agents/chat', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message: value,
        agent_type: 'operations_assistant',
        conversation_id: getConversationId()
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const result = await response.json()

    // 添加AI回复
    const aiMessage: Message = {
      id: Date.now() + 1,
      content: result.response,
      type: 'ai',
      avatar: '/ai-avatar.png',
      status: 'success',
      actions: [
        { key: 'copy', label: '复制', icon: 'copy' },
        { key: 'regenerate', label: '重新生成', icon: 'reload' }
      ]
    }
    messages.value.push(aiMessage)

  } catch (error) {
    console.error('AI对话失败:', error)
    const errorMessage: Message = {
      id: Date.now() + 1,
      content: '抱歉，AI服务暂时不可用，请稍后重试。',
      type: 'ai',
      avatar: '/ai-avatar.png',
      status: 'error',
      actions: [
        { key: 'retry', label: '重试', icon: 'reload' }
      ]
    }
    messages.value.push(errorMessage)
  } finally {
    isLoading.value = false
    inputValue.value = ''
    await nextTick()
    scrollToBottom()
  }
}

// 处理建议点击
const handleSuggestionClick = (suggestion: any) => {
  const suggestionMap: Record<string, string> = {
    alerts: '显示当前所有高危告警信息',
    devices: '检查所有网络设备的运行状态',
    network: '分析当前网络性能和带宽使用情况',
    security: '评估当前系统的安全威胁等级'
  }

  inputValue.value = suggestionMap[suggestion.key] || suggestion.label
  handleSubmit(inputValue.value)
}

// 处理消息操作
const handleActionClick = (action: string, message: Message) => {
  switch (action) {
    case 'copy':
      navigator.clipboard.writeText(message.content)
      break
    case 'regenerate':
      // 重新生成回复
      regenerateResponse(message)
      break
    case 'retry':
      // 重试请求
      retryLastMessage()
      break
  }
}

// 处理输入框操作
const handlePromptAction = (action: string) => {
  switch (action) {
    case 'clear':
      inputValue.value = ''
      break
    case 'voice':
      // 启动语音输入
      startVoiceInput()
      break
  }
}

// 滚动到底部
const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

// 获取会话ID
const getConversationId = () => {
  return sessionStorage.getItem('conversation_id') ||
         (() => {
           const id = `conv_${Date.now()}`
           sessionStorage.setItem('conversation_id', id)
           return id
         })()
}

// 重新生成回复
const regenerateResponse = async (message: Message) => {
  // 实现重新生成逻辑
  console.log('重新生成回复:', message)
}

// 重试最后一条消息
const retryLastMessage = () => {
  const lastUserMessage = messages.value
    .filter(m => m.type === 'user')
    .pop()

  if (lastUserMessage) {
    handleSubmit(lastUserMessage.content)
  }
}

// 启动语音输入
const startVoiceInput = () => {
  // 实现语音输入功能
  console.log('启动语音输入')
}
</script>

<style scoped>
.ai-chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fafafa;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  scroll-behavior: smooth;
}

.input-container {
  padding: 16px;
  background: white;
  border-top: 1px solid #e8e8e8;
}
</style>
```

#### 6.1.4 后端OpenAI Agents实现
```python
from agents import Agent, Runner, function_tool
from agents.extensions.models.litellm_model import LitellmModel
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
import asyncio

class ChatRequest(BaseModel):
    message: str
    agent_type: str = "operations_assistant"

class ChatResponse(BaseModel):
    response: str
    agent_used: str

# 定义工具函数
@function_tool
async def get_device_status(device_id: str) -> str:
    """获取设备状态信息

    Args:
        device_id: 设备ID
    """
    # 实际实现中会查询数据库
    return f"设备 {device_id} 状态正常，CPU使用率: 45%, 内存使用率: 60%"

@function_tool
async def query_alerts(severity: str = "high") -> str:
    """查询告警信息

    Args:
        severity: 告警级别 (high, medium, low)
    """
    # 实际实现中会查询告警数据库
    return f"当前有3个{severity}级别告警：网络延迟异常、磁盘空间不足、CPU温度过高"

# 创建运维助手Agent，使用DeepSeek V3模型
operations_agent = Agent(
    name="Operations Assistant",
    instructions="""你是校园网络运维专家。你可以：
    1. 查询设备状态和告警信息
    2. 分析网络安全威胁
    3. 提供运维建议和故障诊断
    4. 用中文回答用户问题

    请根据用户的问题调用相应的工具函数获取信息，然后提供专业的分析和建议。""",
    model=LitellmModel(
        model="deepseek/deepseek-chat",
        api_key="your_deepseek_api_key"
    ),
    tools=[get_device_status, query_alerts]
)

router = APIRouter()

@router.post("/agents/chat", response_model=ChatResponse)
async def chat_with_agent(request: ChatRequest):
    try:
        # 使用OpenAI Agents运行对话
        result = await Runner.run(
            starting_agent=operations_agent,
            input=request.message
        )

        return ChatResponse(
            response=result.final_output,
            agent_used="operations_assistant"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"AI对话失败: {str(e)}")
```

### 6.2 网络协议实现规格

#### 6.2.1 SNMP监控详细配置
```yaml
# SNMP配置示例
snmp_config:
  version: "2c"
  community: "public"
  timeout: 5
  retries: 3

# 关键OID监控列表
monitoring_oids:
  system_info:
    - "*******.2.1.1.1.0"  # sysDescr
    - "*******.2.1.1.3.0"  # sysUpTime
    - "*******.2.1.1.5.0"  # sysName

  interface_stats:
    - "*******.*******.1.10"  # ifInOctets
    - "*******.*******.1.16"  # ifOutOctets
    - "*******.*******.1.8"   # ifOperStatus

  performance_metrics:
    - "*******.*******.*********.1.5"  # cpmCPUTotal5min
    - "*******.*******.********.5"     # ciscoMemoryPoolUsed
```

#### 6.2.2 网络设备发现算法
```python
# 设备发现流程
discovery_process:
  1. ARP表扫描：
     - 扫描频率：每小时一次
     - 扫描范围：配置的网段
     - 输出：IP-MAC映射表

  2. SNMP Walk：
     - 目标：ARP表中的IP地址
     - 检测：SNMP响应和设备类型
     - 超时：5秒

  3. 端口扫描：
     - 常用端口：22,23,80,443,161,162
     - 识别：设备类型和服务
     - 工具：python3-nmap

  4. 设备分类：
     - 交换机：支持BRIDGE-MIB
     - 路由器：支持IP-FORWARD-MIB
     - 无线AP：支持IEEE802dot11-MIB
```

### 6.3 数据存储详细设计

#### 6.3.1 MySQL统一存储表结构设计
```sql
-- 设备信息表
CREATE TABLE devices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    ip_address VARCHAR(15) NOT NULL UNIQUE,
    mac_address VARCHAR(17),
    device_type ENUM('switch', 'router', 'ap', 'server'),
    vendor VARCHAR(50),
    model VARCHAR(100),
    snmp_community VARCHAR(50),
    location VARCHAR(100),
    group_id INT,
    status ENUM('online', 'offline', 'unknown'),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_ip (ip_address),
    INDEX idx_type (device_type),
    INDEX idx_status (status)
);

-- 实时监控数据表（替代Redis缓存）
CREATE TABLE real_time_metrics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    device_id INT NOT NULL,
    metric_type VARCHAR(50) NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,2),
    unit VARCHAR(20),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,  -- TTL机制
    INDEX idx_device_metric (device_id, metric_type, metric_name),
    INDEX idx_expires (expires_at),
    FOREIGN KEY (device_id) REFERENCES devices(id)
) ENGINE=MEMORY;  -- 使用内存引擎提高性能

-- 历史监控数据表（分区表）
CREATE TABLE monitoring_data (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    device_id INT NOT NULL,
    metric_type VARCHAR(50) NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,2),
    unit VARCHAR(20),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_device_time (device_id, timestamp),
    INDEX idx_metric (metric_type, metric_name),
    FOREIGN KEY (device_id) REFERENCES devices(id)
) PARTITION BY RANGE (UNIX_TIMESTAMP(timestamp)) (
    PARTITION p_current VALUES LESS THAN (UNIX_TIMESTAMP('2025-02-01')),
    PARTITION p_202502 VALUES LESS THAN (UNIX_TIMESTAMP('2025-03-01')),
    PARTITION p_202503 VALUES LESS THAN (UNIX_TIMESTAMP('2025-04-01')),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 告警记录表
CREATE TABLE alerts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    device_id INT,
    alert_type VARCHAR(50) NOT NULL,
    severity ENUM('critical', 'high', 'medium', 'low'),
    title VARCHAR(200) NOT NULL,
    description TEXT,
    deepseek_analysis TEXT,  -- DeepSeek分析结果
    status ENUM('open', 'acknowledged', 'resolved', 'closed'),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP NULL,
    INDEX idx_device (device_id),
    INDEX idx_severity (severity),
    INDEX idx_status (status),
    INDEX idx_created (created_at),
    FOREIGN KEY (device_id) REFERENCES devices(id)
);



-- 配置缓存表
CREATE TABLE config_cache (
    cache_key VARCHAR(255) PRIMARY KEY,
    cache_value JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    INDEX idx_expires (expires_at)
) ENGINE=MEMORY;

-- AI分析缓存表
CREATE TABLE ai_analysis_cache (
    query_hash VARCHAR(64) PRIMARY KEY,
    query_text TEXT,
    analysis_result JSON,
    model_version VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    INDEX idx_expires (expires_at)
);
```

#### 6.3.2 数据库性能优化策略
```python
# 数据库连接池配置
DATABASE_CONFIG = {
    "host": "localhost",
    "port": 3306,
    "database": "campusguard",
    "charset": "utf8mb4",
    "pool_size": 20,
    "max_overflow": 30,
    "pool_timeout": 30,
    "pool_recycle": 3600,
    "echo": False
}

# 自动清理过期数据
CLEANUP_TASKS = {
    "real_time_metrics": "DELETE FROM real_time_metrics WHERE expires_at < NOW()",
    "config_cache": "DELETE FROM config_cache WHERE expires_at < NOW()",
    "ai_analysis_cache": "DELETE FROM ai_analysis_cache WHERE expires_at < NOW()"
}

# 分区管理策略
PARTITION_MANAGEMENT = {
    "auto_create": True,
    "retention_months": 12,
    "cleanup_schedule": "0 2 * * *"  # 每天凌晨2点清理
}
```

### 6.4 API接口详细规格

#### 6.4.1 RESTful API设计
```yaml
# API版本控制
api_version: "v1"
base_url: "/api/v1"

# 设备管理API
devices:
  list:
    method: GET
    path: "/devices"
    parameters:
      - name: page
        type: integer
        default: 1
      - name: size
        type: integer
        default: 20
      - name: device_type
        type: string
        enum: [switch, router, ap, server]
    response:
      type: object
      properties:
        data:
          type: array
          items: DeviceSchema
        total: integer
        page: integer
        size: integer

  create:
    method: POST
    path: "/devices"
    request_body: DeviceCreateSchema
    response: DeviceSchema

  update:
    method: PUT
    path: "/devices/{device_id}"
    request_body: DeviceUpdateSchema
    response: DeviceSchema

  delete:
    method: DELETE
    path: "/devices/{device_id}"
    response: SuccessSchema

# OpenAI Agents AI分析API
agents_analysis:
  analyze_threat:
    method: POST
    path: "/agents/analyze/threat"
    request_body:
      type: object
      properties:
        device_id: integer
        time_range: string
        data_type: string
        analysis_type: string
        enum: [security, performance, anomaly]
        agent_type: string
        enum: [security_analyst, operations_assistant]
    response: AgentsAnalysisSchema

  chat_query:
    method: POST
    path: "/agents/chat"
    request_body:
      type: object
      properties:
        message: string
        agent_type: string
        enum: [security_analyst, operations_assistant, alert_explainer]
        context: object
    response: AgentsChatResponseSchema

  batch_analysis:
    method: POST
    path: "/agents/batch/analyze"
    request_body:
      type: object
      properties:
        requests: array
        priority: string
        enum: [high, medium, low]
        agent_type: string
    response: BatchAnalysisResponseSchema

# 实时数据API
realtime:
  get_metrics:
    method: GET
    path: "/realtime/metrics/{device_id}"
    parameters:
      - name: metric_types
        type: array
        items: string
      - name: time_window
        type: integer
        default: 300  # 5分钟
    response: RealTimeMetricsSchema

  get_device_status:
    method: GET
    path: "/realtime/status"
    parameters:
      - name: device_ids
        type: array
        items: integer
    response: DeviceStatusSchema
```

#### 6.4.2 WebSocket实时推送
```javascript
// WebSocket连接配置
websocket_config = {
    url: "ws://localhost:8000/ws",
    protocols: ["fastapi-ws"],
    heartbeat_interval: 30000,
    reconnect_attempts: 5,
    reconnect_delay: 5000
};

// 消息类型定义
message_types = {
    "device_status_update": {
        type: "device_status",
        data: {
            device_id: "integer",
            status: "string",
            timestamp: "datetime"
        }
    },
    "new_alert": {
        type: "alert",
        data: {
            alert_id: "integer",
            severity: "string",
            title: "string",
            device_id: "integer"
        }
    },
    "monitoring_data": {
        type: "monitoring",
        data: {
            device_id: "integer",
            metrics: "object",
            timestamp: "datetime"
        }
    }
};
```

## 7. 部署和运维规格

### 7.1 容器化部署配置

#### 7.1.1 Docker Compose配置
```yaml
version: '3.8'

services:
  web:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=mysql://campusguard:${MYSQL_PASSWORD}@db:3306/campusguard
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - DEEPSEEK_BASE_URL=https://api.deepseek.com/v1
      - PYTHON_ENV=production
    depends_on:
      - db
    volumes:
      - ./logs:/app/logs
      - ./venv:/app/venv  # 虚拟环境挂载
    restart: unless-stopped

  db:
    image: mysql:8.0.40
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=campusguard
      - MYSQL_USER=campusguard
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
      - ./mysql.cnf:/etc/mysql/conf.d/custom.cnf
    ports:
      - "3306:3306"
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password

  nginx:
    image: nginx:1.27.3-alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - ./static:/var/www/static
    depends_on:
      - web
    restart: unless-stopped

  # G6网络拓扑图前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - VUE_APP_API_BASE_URL=http://web:8000/api/v1
    volumes:
      - ./frontend/dist:/app/dist
    restart: unless-stopped

volumes:
  mysql_data:
```

#### 7.1.2 Python虚拟环境配置
```dockerfile
# Dockerfile
FROM python:3.13.2-slim

WORKDIR /app

# 创建虚拟环境
RUN python -m venv /app/venv
ENV PATH="/app/venv/bin:$PATH"

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    default-libmysqlclient-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements.txt并安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 启动命令
CMD ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### 7.1.3 生产环境配置（更新）
```bash
# 环境变量配置
cat > .env << EOF
# 数据库配置
MYSQL_ROOT_PASSWORD=your_root_password
MYSQL_PASSWORD=your_password
DATABASE_URL=mysql://campusguard:your_password@localhost:3306/campusguard

# DeepSeek AI配置
DEEPSEEK_API_KEY=your_deepseek_api_key
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1
DEEPSEEK_MODEL=deepseek-chat

# 应用配置
SECRET_KEY=your_secret_key
DEBUG=false
LOG_LEVEL=INFO
PYTHON_ENV=production



# 前端配置
VUE_APP_API_BASE_URL=https://your-domain.com/api/v1
VUE_APP_WS_URL=wss://your-domain.com/ws
EOF

# Python虚拟环境设置
python3.13 -m venv /opt/campusguard/venv
source /opt/campusguard/venv/bin/activate
pip install -r requirements.txt

# MySQL配置优化
cat > mysql.cnf << EOF
[mysqld]
# 内存配置
innodb_buffer_pool_size = 4G
innodb_log_file_size = 256M
innodb_log_buffer_size = 64M

# 连接配置
max_connections = 200
max_connect_errors = 100000

# 性能优化
innodb_flush_log_at_trx_commit = 2
sync_binlog = 0
query_cache_type = 1
query_cache_size = 256M

# 分区表支持
partition = ON
EOF

# 系统要求
system_requirements:
  cpu: "4 cores minimum, 8 cores recommended"
  memory: "8GB minimum, 16GB recommended (无Redis节省2GB内存)"
  storage: "100GB minimum, 500GB recommended"
  network: "1Gbps network interface"
  os: "Ubuntu 22.04 LTS or CentOS 9"
  python: "Python 3.13.2"
```